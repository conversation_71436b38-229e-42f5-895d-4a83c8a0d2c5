import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useClinicStore } from '../../stores/useClinicStore';
import { useDebounce } from '@/libs/utils/hooks/useDebounce/useDebounce';
import { queryKeys } from '@/libs/query/queryClient';
import { get } from '@/libs/utils/api';

type OrderedProduct = {
  productOfferId: string;
  productId: string;
  productName: string;
  vendorId: string;
  vendorName: string;
  lastOrderedAt: string;
  quantity: number;
  price: number;
  imageUrl: string;
  orderCount: number;
};

export const useProductSuggestions = (
  query = '',
): {
  suggestions: string[];
  previouslyOrderedItems: OrderedProduct[];
  isSuggestionsLoading: boolean;
} => {
  const search = query.trim().toLowerCase();
  const [lastQueryWithNoResults, setLastEmptyQuery] = useState<string>('');
  const { clinic } = useClinicStore();
  const debouncedQuery = useDebounce(search, 300);

  const queryKey = queryKeys.products.suggestions(
    clinic?.id || '',
    debouncedQuery,
  );

  const shouldSkipQuery = () => {
    if (debouncedQuery?.length <= 2 || !clinic?.id) {
      return true;
    }

    const noResultsQuery =
      lastQueryWithNoResults &&
      debouncedQuery.startsWith(lastQueryWithNoResults);

    return noResultsQuery;
  };

  const {
    data = { suggestions: [], previouslyOrderedItems: [] },
    isLoading: isSuggestionsLoading,
  } = useQuery({
    queryKey,
    queryFn: async () => {
      try {
        const response = await get<{
          data: string[];
          previouslyOrderedItems: OrderedProduct[];
        }>({
          url: `/clinics/${clinic?.id}/autocomplete?query=${encodeURIComponent(debouncedQuery)}&clinicId=${clinic?.id}`,
        });

        const suggestions = response?.data || [];

        if (suggestions.length === 0) {
          setLastEmptyQuery(debouncedQuery);
        } else {
          setLastEmptyQuery('');
        }

        return {
          suggestions,
          previouslyOrderedItems: response?.previouslyOrderedItems || [],
        };
      } catch (error) {
        setLastEmptyQuery(debouncedQuery);
        throw error;
      }
    },
    enabled: !shouldSkipQuery(),
    staleTime: 5 * 60 * 1000,
  });

  useEffect(() => {
    const isBackSpacing =
      lastQueryWithNoResults &&
      debouncedQuery.length < lastQueryWithNoResults.length;

    if (isBackSpacing) {
      setLastEmptyQuery('');
    }
  }, [debouncedQuery, lastQueryWithNoResults]);

  return {
    ...data,
    isSuggestionsLoading,
  };
};
